// Simple test to verify the scheduling service syntax
const fs = require('fs');
const path = require('path');

// Read the scheduling service file
const schedulingServicePath = path.join(__dirname, 'src/services/schedulingService.ts');
const content = fs.readFileSync(schedulingServicePath, 'utf8');

// Basic syntax validation
console.log('Checking scheduling service syntax...');

// Check for common syntax errors
const syntaxChecks = [
  { pattern: /import.*from.*['"][^'"]*['"];?/g, name: 'Import statements' },
  { pattern: /export\s+(interface|class|const|function)/g, name: 'Export statements' },
  { pattern: /\{[^}]*\}/g, name: 'Object literals' },
  { pattern: /\([^)]*\)/g, name: 'Function parameters' },
];

let hasErrors = false;

syntaxChecks.forEach(check => {
  const matches = content.match(check.pattern);
  if (matches) {
    console.log(`✓ ${check.name}: ${matches.length} found`);
  } else {
    console.log(`✗ ${check.name}: None found`);
    hasErrors = true;
  }
});

// Check for specific issues we fixed
const fixedIssues = [
  { pattern: /'IN_PROGRESS'/g, name: 'IN_PROGRESS status usage' },
  { pattern: /isRecurring:/g, name: 'isRecurring field usage' },
  { pattern: /recurrencePattern,\s*\n\s*\}\);/g, name: 'recurrencePattern in appointment creation' },
  { pattern: /parse,/g, name: 'Unused parse import' },
  { pattern: /toZonedTime,/g, name: 'Unused toZonedTime import' },
];

console.log('\nChecking for fixed issues:');
fixedIssues.forEach(check => {
  const matches = content.match(check.pattern);
  if (matches) {
    console.log(`⚠ ${check.name}: Still present (${matches.length} occurrences)`);
    hasErrors = true;
  } else {
    console.log(`✓ ${check.name}: Fixed`);
  }
});

if (!hasErrors) {
  console.log('\n✅ All syntax checks passed!');
} else {
  console.log('\n❌ Some issues found');
}
