module.exports = {
  apps: [
    {
      name: 'frontend',
      script: 'npm',
      args: 'run dev',
      cwd: './',
      env: {
        NODE_ENV: 'development',
        PORT: 5173
      },
      watch: false,
      ignore_watch: ['node_modules', 'dist', 'build'],
      log_file: './logs/frontend.log',
      out_file: './logs/frontend-out.log',
      error_file: './logs/frontend-error.log',
      time: true,
      instances: 1,
      autorestart: true,
      max_restarts: 5,
      min_uptime: '10s'
    },
    {
      name: 'backend',
      script: 'npm',
      args: 'run dev',
      cwd: './backend',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      watch: false,
      ignore_watch: ['node_modules', 'dist', 'logs'],
      log_file: './logs/backend.log',
      out_file: './logs/backend-out.log',
      error_file: './logs/backend-error.log',
      time: true,
      instances: 1,
      autorestart: true,
      max_restarts: 5,
      min_uptime: '10s'
    }
  ]
};
