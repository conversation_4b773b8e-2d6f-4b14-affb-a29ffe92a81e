generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                   String         @id @default(cuid())
  email                String?        @unique
  password             String?
  name                 String?
  phone                String?
  profilePicture       String?
  passwordResetToken   String?
  passwordResetExpires DateTime?
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt
  emailVerified        DateTime?
  image                String?
  role                 Role           @default(PATIENT)
  accounts             Account[]
  notifications        Notification[]
  patient              Patient?
  provider             Provider?
  sessions             Session[]
  refreshTokens        RefreshToken[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Patient {
  id                String        @id @default(cuid())
  userId            String        @unique
  dateOfBirth       DateTime?
  address           String?
  phone             String?
  emergencyContact  Json?
  medicalHistory    Json?
  insurance         Json?
  preferences       Json?
  onboardingStatus  String        @default("INCOMPLETE")
  onboardingStep    Int           @default(0)
  consentGiven      Boolean       @default(false)
  consentDate       DateTime?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  appointments      Appointment[]
  medicalRecords    MedicalRecord[]
  emergencyContacts EmergencyContact[]
  allergies         Allergy[]
  medications       Medication[]
  conditions        Condition[]
  insuranceRecords  Insurance[]
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Provider {
  id             String          @id @default(cuid())
  userId         String          @unique
  specialization String?
  bio            String?
  education      String?
  experience     String?
  consultationFee Decimal?
  approvalStatus String          @default("PENDING")
  approvedBy     String?
  approvedAt     DateTime?
  rejectionReason String?
  isActive       Boolean         @default(true)
  isVerified     Boolean         @default(false)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  avatarUrl      String?
  appointments   Appointment[]
  medicalRecords MedicalRecord[]
  availability   Availability[]
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model MedicalRecord {
  id             String   @id @default(cuid())
  patientId      String
  providerId     String
  consultationId String?
  diagnosis      String?
  notes          String?
  prescriptions  Json?
  attachments    Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  patient        Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider       Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)
}

model Availability {
  id          String   @id @default(cuid())
  providerId  String
  dayOfWeek   String   // MONDAY, TUESDAY, etc.
  startTime   String   // HH:MM format
  endTime     String   // HH:MM format
  isAvailable Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  provider    Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@unique([providerId, dayOfWeek, startTime])
}

model Appointment {
  id               String            @id @default(cuid())
  patientId        String
  providerId       String
  appointmentDate  DateTime
  reason           String?
  duration         Int?
  notes            String?
  timezone         String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  consultationType ConsultationType  @default(VIDEO)
  status           AppointmentStatus @default(SCHEDULED)
  patient          Patient           @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider         Provider          @relation(fields: [providerId], references: [id], onDelete: Cascade)
  consultation     Consultation?
  payment          Payment?
}

model Consultation {
  id            String             @id @default(cuid())
  appointmentId String             @unique
  roomUrl       String
  dailyRoomId   String?
  dailyRoomName String?
  notes         String?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  videoEnabled  Boolean            @default(false)
  sessionId     String?            @unique
  status        ConsultationStatus @default(SCHEDULED)
  appointment   Appointment        @relation(fields: [appointmentId], references: [id], onDelete: Cascade)

  @@index([sessionId])
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String
  type      String
  userId    String
  relatedId String?
  isRead    Boolean  @default(false)
  link      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Payment {
  id            String        @id @default(cuid())
  appointmentId String        @unique
  amount        Int
  currency      String        @default("USD")
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  provider      String
  receiptUrl    String?
  reference     String?
  status        PaymentStatus @default(PENDING)
  paymentMethod PaymentMethod @default(STRIPE)
  appointment   Appointment   @relation(fields: [appointmentId], references: [id], onDelete: Cascade)

  @@index([paymentMethod])
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum ConsultationStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ConsultationType {
  VIDEO
  AUDIO
}

enum PaymentMethod {
  STRIPE
  PAYSTACK
  PAYPAL
  FLUTTERWAVE
  CREDIT_CARD
  BANK_TRANSFER
}

enum PaymentStatus {
  INITIATED
  PENDING
  SUCCEEDED
  FAILED
  REFUNDED
}

enum Role {
  PATIENT
  PROVIDER
  DOCTOR
  ADMIN
}

model EmergencyContact {
  id        String  @id @default(cuid())
  patientId String
  name      String
  phone     String
  relationship String
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Allergy {
  id        String  @id @default(cuid())
  patientId String
  allergen  String
  severity  String?
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Medication {
  id        String  @id @default(cuid())
  patientId String
  name      String
  dosage    String?
  frequency String?
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Condition {
  id        String  @id @default(cuid())
  patientId String
  name      String
  diagnosed DateTime?
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Insurance {
  id        String  @id @default(cuid())
  patientId String
  provider  String
  policyNumber String
  groupNumber String?
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}
