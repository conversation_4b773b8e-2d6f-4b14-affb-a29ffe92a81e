import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Heart, 
  Activity, 
  TrendingUp, 
  TrendingDown,
  Minus,
  Target,
  Calendar,
  Clock,
  Plus
} from 'lucide-react';
import { format } from 'date-fns';

interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  target?: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage?: number;
  lastUpdated: Date;
  status: 'excellent' | 'good' | 'fair' | 'poor';
  category: 'vitals' | 'fitness' | 'mental' | 'nutrition';
}

interface HealthMetricsProps {
  metrics: HealthMetric[];
  isLoading?: boolean;
  onAddMetric?: () => void;
  onViewDetails?: (metricId: string) => void;
}

const HealthMetrics: React.FC<HealthMetricsProps> = ({ 
  metrics, 
  isLoading = false, 
  onAddMetric,
  onViewDetails 
}) => {
  const getTrendIcon = (trend: HealthMetric['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      case 'stable':
      default:
        return <Minus className="h-3 w-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status: HealthMetric['status']) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'good':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'fair':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'poor':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getCategoryIcon = (category: HealthMetric['category']) => {
    switch (category) {
      case 'vitals':
        return <Heart className="h-4 w-4" />;
      case 'fitness':
        return <Activity className="h-4 w-4" />;
      case 'mental':
        return <Target className="h-4 w-4" />;
      case 'nutrition':
        return <Calendar className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const calculateProgress = (metric: HealthMetric) => {
    if (!metric.target) return 0;
    return Math.min((metric.value / metric.target) * 100, 100);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Health Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                    <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (metrics.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              Health Metrics
            </CardTitle>
            {onAddMetric && (
              <Button variant="outline" size="sm" onClick={onAddMetric}>
                <Plus className="h-4 w-4 mr-1" />
                Add Metric
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No health metrics recorded</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              Start tracking your health data to see insights here
            </p>
            {onAddMetric && (
              <Button variant="outline" className="mt-4" onClick={onAddMetric}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Metric
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Health Metrics
          </CardTitle>
          {onAddMetric && (
            <Button variant="outline" size="sm" onClick={onAddMetric}>
              <Plus className="h-4 w-4 mr-1" />
              Add Metric
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {metrics.map((metric) => (
            <div
              key={metric.id}
              className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer"
              onClick={() => onViewDetails?.(metric.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(metric.category)}
                  <span className="font-medium text-sm text-gray-700 dark:text-gray-300">
                    {metric.name}
                  </span>
                </div>
                <Badge variant="outline" className={getStatusColor(metric.status)}>
                  {metric.status}
                </Badge>
              </div>

              <div className="flex items-baseline gap-2 mb-3">
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {metric.value}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {metric.unit}
                </span>
                <div className="flex items-center gap-1 ml-auto">
                  {getTrendIcon(metric.trend)}
                  {metric.trendPercentage && (
                    <span className={`text-xs ${
                      metric.trend === 'up' ? 'text-green-600' : 
                      metric.trend === 'down' ? 'text-red-600' : 
                      'text-gray-500'
                    }`}>
                      {metric.trendPercentage}%
                    </span>
                  )}
                </div>
              </div>

              {metric.target && (
                <div className="mb-3">
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <span>Progress to target</span>
                    <span>{metric.target} {metric.unit}</span>
                  </div>
                  <Progress 
                    value={calculateProgress(metric)} 
                    className="h-2"
                  />
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>Updated {format(metric.lastUpdated, 'MMM d')}</span>
                </div>
                <span className="capitalize">{metric.category}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default HealthMetrics;
