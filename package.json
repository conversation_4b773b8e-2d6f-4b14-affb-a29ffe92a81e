{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:frontend": "vite", "dev:backend": "cd backend && npm run dev", "dev:all": "concurrently --kill-others --prefix \"[{name}]\" --names \"BACKEND,FRONTEND\" --prefix-colors \"bgBlue.bold,bgGreen.bold\" \"npm run dev:backend\" \"npm run dev:frontend\"", "build": "prisma generate && prisma migrate deploy && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "validate-config": "node validate-config.js", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ci": "vitest run --coverage", "test:ui": "vitest --ui", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma generate && prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node --esm prisma/seed.ts", "server:install": "cd server && npm install", "server:dev": "cd server && npm run dev", "server:start": "cd server && npm start", "server:build": "cd server && npm run build", "full:install": "npm install && npm run server:install", "full:dev": "concurrently --kill-others --prefix \"[{name}]\" --names \"FRONTEND,SERVER\" --prefix-colors \"bgGreen.bold,bgMagenta.bold\" \"npm run dev\" \"npm run server:dev\"", "full:build": "npm run build && npm run server:build", "deploy:setup": "npm run full:install && npm run prisma:generate && npm run prisma:deploy", "dev:check": "concurrently --success first --kill-others \"npm run dev:backend -- --check\" \"npm run dev:frontend -- --check\"", "dev:verbose": "concurrently --kill-others --prefix \"[{name}]\" --names \"BACKEND,FRONTEND\" --prefix-colors \"bgBlue.bold,bgGreen.bold\" --timestamp-format \"HH:mm:ss\" \"npm run dev:backend\" \"npm run dev:frontend\"", "status": "echo '🔍 Checking if services are running...' && (curl -s http://localhost:5173 > /dev/null && echo '✅ Frontend: http://localhost:5173' || echo '❌ Frontend: Not running') && (curl -s http://localhost:3000/api/health > /dev/null && echo '✅ Backend: http://localhost:3000' || echo '❌ Backend: Not running')"}, "dependencies": {"@daily-co/daily-js": "^0.80.0", "@hookform/resolvers": "^3.9.0", "@neondatabase/serverless": "^1.0.1", "@paystack/inline-js": "^2.13.0", "@prisma/adapter-neon": "^6.9.0", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "^5.56.2", "@types/bcrypt": "^5.0.2", "add-to-calendar-button-react": "^2.9.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "notificationapi-node-server-sdk": "^2.5.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-google-calendar-api": "^2.3.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "scroll-behavior-polyfill": "^2.0.13", "simple-peer": "^9.11.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.8", "nodemon": "^3.1.10", "postcss": "^8.4.47", "prisma": "^6.9.0", "tailwindcss": "^3.4.11", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.4", "workbox-window": "^7.3.0"}, "trustedDependencies": ["@swc/core"], "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}