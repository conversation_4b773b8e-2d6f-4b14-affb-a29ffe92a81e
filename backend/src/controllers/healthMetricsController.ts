import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '@/types';

const prisma = new PrismaClient();

interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  target?: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage?: number;
  lastUpdated: Date;
  status: 'excellent' | 'good' | 'fair' | 'poor';
  category: 'vitals' | 'fitness' | 'mental' | 'nutrition';
}

/**
 * Calculate trend based on recent values
 */
const calculateTrend = (currentValue: number, previousValue?: number): { trend: 'up' | 'down' | 'stable', percentage: number } => {
  if (!previousValue) {
    return { trend: 'stable', percentage: 0 };
  }

  const change = currentValue - previousValue;
  const percentage = Math.abs((change / previousValue) * 100);

  if (Math.abs(change) < 0.01) {
    return { trend: 'stable', percentage: 0 };
  }

  return {
    trend: change > 0 ? 'up' : 'down',
    percentage: Math.round(percentage * 10) / 10
  };
};

/**
 * Determine health status based on value and target
 */
const getHealthStatus = (value: number, target?: number, category?: string): 'excellent' | 'good' | 'fair' | 'poor' => {
  if (!target) {
    // Default status based on common health ranges
    if (category === 'vitals') {
      return 'good'; // Default for vitals
    }
    return value >= 80 ? 'excellent' : value >= 60 ? 'good' : value >= 40 ? 'fair' : 'poor';
  }

  const deviation = Math.abs(value - target) / target;
  
  if (deviation <= 0.05) return 'excellent';
  if (deviation <= 0.15) return 'good';
  if (deviation <= 0.30) return 'fair';
  return 'poor';
};

/**
 * Get patient health metrics
 */
export const getPatientHealthMetrics = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    // Get user's patient record
    const patient = await prisma.patient.findUnique({
      where: { userId },
      select: { id: true }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient record not found'
      });
    }

    // Get latest health metrics for each metric name
    const latestMetrics = await prisma.healthMetric.findMany({
      where: { patientId: patient.id },
      orderBy: { recordedAt: 'desc' },
      take: 50 // Get recent metrics to calculate trends
    });

    // Group by metric name and get the latest for each
    const metricGroups = latestMetrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric);
      return acc;
    }, {} as Record<string, typeof latestMetrics>);

    const healthMetrics: HealthMetric[] = [];

    // Process each metric group
    Object.entries(metricGroups).forEach(([metricName, metrics]) => {
      const sortedMetrics = metrics.sort((a, b) => 
        new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime()
      );
      
      const latest = sortedMetrics[0];
      const previous = sortedMetrics[1];

      const { trend, percentage } = calculateTrend(
        latest.value, 
        previous?.value
      );

      const status = getHealthStatus(
        latest.value, 
        latest.target || undefined, 
        latest.category
      );

      healthMetrics.push({
        id: latest.id,
        name: latest.name,
        value: latest.value,
        unit: latest.unit,
        target: latest.target || undefined,
        trend,
        trendPercentage: percentage,
        lastUpdated: latest.recordedAt,
        status,
        category: latest.category as any
      });
    });

    // If no metrics exist, create some sample data for demo
    if (healthMetrics.length === 0) {
      const sampleMetrics = await createSampleHealthMetrics(patient.id);
      return res.json({
        success: true,
        data: sampleMetrics
      });
    }

    res.json({
      success: true,
      data: healthMetrics
    });

  } catch (error) {
    console.error('Error fetching health metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch health metrics'
    });
  }
};

/**
 * Create a new health metric entry
 */
export const createHealthMetric = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    // Get user's patient record
    const patient = await prisma.patient.findUnique({
      where: { userId },
      select: { id: true }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient record not found'
      });
    }

    const { name, value, unit, target, category } = req.body;

    if (!name || value === undefined || !unit || !category) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, value, unit, category'
      });
    }

    const status = getHealthStatus(value, target, category);

    const healthMetric = await prisma.healthMetric.create({
      data: {
        patientId: patient.id,
        name,
        value: parseFloat(value),
        unit,
        target: target ? parseFloat(target) : null,
        category,
        status
      }
    });

    res.status(201).json({
      success: true,
      data: healthMetric
    });

  } catch (error) {
    console.error('Error creating health metric:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create health metric'
    });
  }
};

/**
 * Get health metrics by category
 */
export const getHealthMetricsByCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { category } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    const patient = await prisma.patient.findUnique({
      where: { userId },
      select: { id: true }
    });

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient record not found'
      });
    }

    const metrics = await prisma.healthMetric.findMany({
      where: { 
        patientId: patient.id,
        category 
      },
      orderBy: { recordedAt: 'desc' },
      take: 20
    });

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    console.error('Error fetching health metrics by category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch health metrics'
    });
  }
};

/**
 * Create sample health metrics for demo purposes
 */
const createSampleHealthMetrics = async (patientId: string): Promise<HealthMetric[]> => {
  const now = new Date();
  
  const sampleData = [
    {
      name: 'Blood Pressure',
      value: 120,
      unit: 'mmHg',
      target: 120,
      category: 'vitals'
    },
    {
      name: 'Heart Rate',
      value: 72,
      unit: 'bpm',
      target: 70,
      category: 'vitals'
    },
    {
      name: 'Weight',
      value: 165,
      unit: 'lbs',
      target: 160,
      category: 'fitness'
    },
    {
      name: 'Sleep Quality',
      value: 85,
      unit: '%',
      target: 90,
      category: 'mental'
    }
  ];

  const createdMetrics = await Promise.all(
    sampleData.map(async (data) => {
      const status = getHealthStatus(data.value, data.target, data.category);
      
      return await prisma.healthMetric.create({
        data: {
          patientId,
          name: data.name,
          value: data.value,
          unit: data.unit,
          target: data.target,
          category: data.category,
          status,
          recordedAt: new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000) // Random time in last 24h
        }
      });
    })
  );

  return createdMetrics.map(metric => ({
    id: metric.id,
    name: metric.name,
    value: metric.value,
    unit: metric.unit,
    target: metric.target || undefined,
    trend: 'stable' as const,
    trendPercentage: 0,
    lastUpdated: metric.recordedAt,
    status: metric.status as any,
    category: metric.category as any
  }));
};
