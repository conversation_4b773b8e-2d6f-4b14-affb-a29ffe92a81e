#!/usr/bin/env node

/**
 * Development Services Status Checker
 * Verifies that both frontend and backend services are running
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const SERVICES = {
  frontend: {
    name: 'Frontend (Vite)',
    url: 'http://localhost:5173',
    healthCheck: 'http://localhost:5173',
    color: '\x1b[32m', // Green
  },
  backend: {
    name: 'Backend (Express)',
    url: 'http://localhost:3000',
    healthCheck: 'http://localhost:3000/api/health',
    color: '\x1b[34m', // Blue
  }
};

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

async function checkService(service) {
  try {
    const response = await fetch(service.healthCheck, {
      method: 'GET',
      timeout: 3000,
    });
    
    if (response.ok) {
      return {
        status: 'running',
        message: `✅ ${service.name} is running on ${service.url}`,
        color: COLORS.green
      };
    } else {
      return {
        status: 'error',
        message: `⚠️  ${service.name} responded with status ${response.status}`,
        color: COLORS.yellow
      };
    }
  } catch (error) {
    return {
      status: 'down',
      message: `❌ ${service.name} is not running (${service.url})`,
      color: COLORS.red
    };
  }
}

async function checkProcesses() {
  try {
    const { stdout } = await execAsync('ps aux | grep -E "(vite|node.*server|nodemon)" | grep -v grep');
    const processes = stdout.split('\n').filter(line => line.trim());
    
    console.log(`\n${COLORS.cyan}${COLORS.bright}🔍 Running Processes:${COLORS.reset}`);
    processes.forEach(process => {
      const parts = process.split(/\s+/);
      if (parts.length > 10) {
        const command = parts.slice(10).join(' ');
        console.log(`   ${COLORS.yellow}•${COLORS.reset} ${command}`);
      }
    });
  } catch (error) {
    console.log(`\n${COLORS.yellow}⚠️  Could not check running processes${COLORS.reset}`);
  }
}

async function main() {
  console.log(`${COLORS.cyan}${COLORS.bright}🚀 Development Services Status Check${COLORS.reset}\n`);
  
  const results = await Promise.all([
    checkService(SERVICES.frontend),
    checkService(SERVICES.backend)
  ]);
  
  results.forEach(result => {
    console.log(`${result.color}${result.message}${COLORS.reset}`);
  });
  
  await checkProcesses();
  
  const allRunning = results.every(r => r.status === 'running');
  
  if (allRunning) {
    console.log(`\n${COLORS.green}${COLORS.bright}🎉 All services are running successfully!${COLORS.reset}`);
    console.log(`\n${COLORS.cyan}📱 Frontend: ${COLORS.reset}http://localhost:5173`);
    console.log(`${COLORS.cyan}🔧 Backend:  ${COLORS.reset}http://localhost:3000`);
    console.log(`${COLORS.cyan}📊 API Docs: ${COLORS.reset}http://localhost:3000/api-docs`);
  } else {
    console.log(`\n${COLORS.red}${COLORS.bright}⚠️  Some services are not running${COLORS.reset}`);
    console.log(`\n${COLORS.yellow}💡 To start all services:${COLORS.reset}`);
    console.log(`   ${COLORS.cyan}npm run dev:all${COLORS.reset}`);
  }
  
  console.log(`\n${COLORS.magenta}🔄 To check again: ${COLORS.reset}npm run status\n`);
}

main().catch(console.error);
