// Database Integration Test Component
import React, { useState } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2, Database, Users, Calendar, CreditCard } from 'lucide-react';

interface DatabaseTest {
  name: string;
  description: string;
  icon: React.ReactNode;
  testFn: () => Promise<{ success: boolean; message: string; details?: Record<string, unknown> }>;
}

interface TestResult {
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  details?: Record<string, unknown>;
  duration?: number;
}

const DatabaseIntegrationTest: React.FC = () => {
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [isRunning, setIsRunning] = useState(false);

  const updateTestResult = (testName: string, result: Partial<TestResult>) => {
    setTestResults(prev => ({
      ...prev,
      [testName]: { ...prev[testName], ...result }
    }));
  };

  // Database integration tests
  const databaseTests: DatabaseTest[] = [
    {
      name: 'connection',
      description: 'Test database connectivity',
      icon: <Database className="h-4 w-4" />,
      testFn: async () => {
        // Test database connection via health endpoint
        const response = await fetch('/api/health');
        if (!response.ok) {
          throw new Error('Health check failed');
        }
        
        const data = await response.json();
        return {
          success: true,
          message: 'Database connection successful',
          details: data
        };
      }
    },
    {
      name: 'users-table',
      description: 'Test users table operations',
      icon: <Users className="h-4 w-4" />,
      testFn: async () => {
        // Test user-related endpoints to verify table structure
        try {
          const response = await fetch('/api/auth/profile');
          // Expected to fail with 401, but should not fail with database errors
          
          if (response.status === 401) {
            return {
              success: true,
              message: 'Users table accessible (authentication required)',
              details: { statusCode: response.status }
            };
          }
          
          return {
            success: true,
            message: 'Users table operations working',
            details: { statusCode: response.status }
          };
        } catch (error) {
          if (error instanceof Error && error.message.includes('fetch')) {
            throw new Error('Database connection failed');
          }
          throw error;
        }
      }
    },
    {
      name: 'appointments-table',
      description: 'Test appointments table operations',
      icon: <Calendar className="h-4 w-4" />,
      testFn: async () => {
        try {
          const response = await fetch('/api/appointments');
          
          if (response.status === 401) {
            return {
              success: true,
              message: 'Appointments table accessible (authentication required)',
              details: { statusCode: response.status }
            };
          }
          
          return {
            success: true,
            message: 'Appointments table operations working',
            details: { statusCode: response.status }
          };
        } catch (error) {
          if (error instanceof Error && error.message.includes('fetch')) {
            throw new Error('Database connection failed');
          }
          throw error;
        }
      }
    },
    {
      name: 'payments-table',
      description: 'Test payments table operations',
      icon: <CreditCard className="h-4 w-4" />,
      testFn: async () => {
        try {
          const response = await fetch('/api/payments');
          
          if (response.status === 401) {
            return {
              success: true,
              message: 'Payments table accessible (authentication required)',
              details: { statusCode: response.status }
            };
          }
          
          return {
            success: true,
            message: 'Payments table operations working',
            details: { statusCode: response.status }
          };
        } catch (error) {
          if (error instanceof Error && error.message.includes('fetch')) {
            throw new Error('Database connection failed');
          }
          throw error;
        }
      }
    },
    {
      name: 'schema-validation',
      description: 'Test database schema validation',
      icon: <Database className="h-4 w-4" />,
      testFn: async () => {
        // Test schema validation by attempting operations that should validate data
        try {
          // Test with invalid data to ensure validation works
          const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: 'invalid-email', // Should fail validation
              password: '123', // Should fail validation
            })
          });
          
          if (response.status === 400) {
            return {
              success: true,
              message: 'Schema validation working correctly',
              details: { statusCode: response.status }
            };
          }
          
          return {
            success: false,
            message: 'Schema validation may not be working',
            details: { statusCode: response.status }
          };
        } catch (error) {
          throw new Error('Schema validation test failed');
        }
      }
    },
    {
      name: 'transactions',
      description: 'Test database transaction support',
      icon: <Database className="h-4 w-4" />,
      testFn: async () => {
        // Test transaction support by checking payment creation flow
        try {
          const response = await fetch('/api/payments/intent', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              appointmentId: 'test-id',
              amount: 100,
              currency: 'USD',
              paymentMethod: 'STRIPE'
            })
          });
          
          // Should fail with 401 (auth required) not 500 (database error)
          if (response.status === 401) {
            return {
              success: true,
              message: 'Transaction support working (authentication required)',
              details: { statusCode: response.status }
            };
          }
          
          return {
            success: true,
            message: 'Transaction support available',
            details: { statusCode: response.status }
          };
        } catch (error) {
          throw new Error('Transaction test failed');
        }
      }
    }
  ];

  const runDatabaseTests = async () => {
    setIsRunning(true);
    setTestResults({});

    for (const test of databaseTests) {
      updateTestResult(test.name, { status: 'running', message: 'Running...' });
      
      const startTime = Date.now();
      
      try {
        const result = await test.testFn();
        const duration = Date.now() - startTime;
        
        updateTestResult(test.name, {
          status: result.success ? 'success' : 'error',
          message: result.message,
          details: result.details,
          duration
        });
      } catch (error) {
        const duration = Date.now() - startTime;
        
        updateTestResult(test.name, {
          status: 'error',
          message: error instanceof Error ? error.message : 'Test failed',
          duration
        });
      }
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return 'blue';
      case 'success':
        return 'green';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  const completedTests = Object.values(testResults).filter(r => r.status !== 'running').length;
  const passedTests = Object.values(testResults).filter(r => r.status === 'success').length;
  const failedTests = Object.values(testResults).filter(r => r.status === 'error').length;

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Database Integration Test</h1>
        <p className="text-muted-foreground mt-2">
          Test database connectivity, schema validation, and table operations
        </p>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Database Tests
            <Button 
              onClick={runDatabaseTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4" />
                  Run Database Tests
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(testResults).length > 0 && (
            <div className="grid grid-cols-3 gap-4 text-center mb-4">
              <div>
                <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                <div className="text-sm text-muted-foreground">Passed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{databaseTests.length}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {databaseTests.map((test) => {
              const result = testResults[test.name];
              
              return (
                <div key={test.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {test.icon}
                    <div>
                      <div className="font-medium">{test.description}</div>
                      {result?.message && (
                        <div className="text-sm text-muted-foreground">
                          {result.message}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {result?.duration && (
                      <span className="text-xs text-muted-foreground">
                        {result.duration}ms
                      </span>
                    )}
                    {result?.status && (
                      <Badge variant="outline" className={`text-${getStatusColor(result.status)}-600`}>
                        {result.status.toUpperCase()}
                      </Badge>
                    )}
                    {getStatusIcon(result?.status || 'pending')}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Database Status Summary */}
      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Database Integration Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                Database integration testing completed. {passedTests} tests passed, {failedTests} tests failed.
                {failedTests === 0 ? 
                  ' Database is properly configured and accessible!' : 
                  ' Some database issues were detected and should be investigated.'}
              </AlertDescription>
            </Alert>

            {failedTests === 0 && passedTests > 0 && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">✅ Database Health Check Passed</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Database connectivity verified</li>
                  <li>• All table operations accessible</li>
                  <li>• Schema validation working</li>
                  <li>• Transaction support confirmed</li>
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DatabaseIntegrationTest;
