import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getPatientHealthMetrics,
  createHealthMetric,
  getHealthMetricsByCategory
} from '../controllers/healthMetricsController';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

/**
 * @route GET /api/patients/health-metrics
 * @desc Get patient health metrics
 * @access Private (Patient)
 */
router.get('/', getPatientHealthMetrics);

/**
 * @route POST /api/patients/health-metrics
 * @desc Create a new health metric entry
 * @access Private (Patient)
 */
router.post('/', createHealthMetric);

/**
 * @route GET /api/patients/health-metrics/:category
 * @desc Get health metrics by category (vitals, fitness, mental, nutrition)
 * @access Private (Patient)
 */
router.get('/:category', getHealthMetricsByCategory);

export default router;
