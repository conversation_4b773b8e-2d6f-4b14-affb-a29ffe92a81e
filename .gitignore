# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules/
node_modules

# Build outputs
dist/
dist-ssr/
dist-server/
build/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env

# TaskMaster AI files
.taskmaster/
.kilocode/
.cursor/
.roo/
.roomodes
.windsurfrules

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Development files
dev-debug.log
validate-config.js

# OS specific files
Thumbs.db

# Added by Task Master AI
# Dependency directories
.vscode
# OS specific

# Task files
# tasks.json
# tasks/ 
